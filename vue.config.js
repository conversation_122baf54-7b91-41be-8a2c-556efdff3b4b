/*
 * @Author: 秀儿
 * @Date: 2022-03-02 16:17:33
 * @LastEditors: 为了新中国
 * @LastEditTime: 2023-10-07 11:00:10
 * @FilePath: \xpaas-ui-child-zlkgl\vue.config.js
 * @filePurpose:
 */
//引入path模块
const path = require("path");
const resolve = (dir) => path.join(__dirname, dir);
let HtmlWebpackPlugin = require("html-webpack-plugin");


let target = "http://192.168.100.114:20602"; // 对应项目的开发环境
let globalThird = "http://192.168.100.114:20600/globalThird/"; //线上地址 每个项目测试环境地址加globalThird

//包名是从环境变量中获取的
let appName = process.env.VUE_APP_NAME;
module.exports = {
  //路径前缀
  outputDir: appName,
  publicPath: process.env.NODE_ENV === "production" ? "/child/" + appName : "/",
  lintOnSave: true,
  productionSourceMap: false,
  chainWebpack: (config) => {
    //忽略的打包文件
    config.externals({
      vue: "Vue",
      "vue-router": "VueRouter",
      vuex: "Vuex",
      axios: "axios",
      "element-ui": "ELEMENT",
    });
    config.resolve.alias.set("@global", resolve("src/global"));
  },
  devServer: {
    port: 1892,
    proxy: {
      "/api/flowable-engine": {
        target: target,
        ws: true,
        pathRewrite: {
          "^/api/flowable-engine": "/flowable-engine",
        },
      },
      "/api": {
        target: target,
        ws: true,
        pathRewrite: {
          "^/api": "/",
        },
      },
    },
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
  },
  configureWebpack: {
    plugins: [
      new HtmlWebpackPlugin({
        template: "./public/index.html",
        //title: "globalThird",
        //globalThird 存放在基座的根目录
        globalThirdUrl:
          process.env.NODE_ENV === "production" ? "/globalThird/" : globalThird,
        //子应用自己项目的根目录 需要用到自己的根目录时 添加的设置
        baseUrl:
          process.env.NODE_ENV === "production" ? `/child/${appName}/` : "/",
      }),
    ],
    output: {
      library: appName,
      libraryTarget: "umd",
    },
  },
};
