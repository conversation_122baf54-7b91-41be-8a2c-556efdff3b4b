/*
 * @Author: 碎颅锤本锤
 * @Date: 2022-03-03 16:55:41
 * @LastEditors: 秀儿
 * @LastEditTime: 2022-03-27 14:05:09
 * @FilePath: \xpaas-ui-child-05-manage\src\views\application\mixins\crudTree.js
 * @filePurpose: mixins文件  带有树的
 */
export const crudTree = {
    data() {
        const sort = (rule, value, callback) => {
            let zhReg = /^[0-9]*[0-9][0-9]*$/;
            let a = /^\d{1,10}$/g;
            if (value === "") {
                callback(new Error("请输入排序"));
            } else if (!zhReg.test(value)) {
                callback(new Error("排序只能是非负整数"));
            } else if (!a.test(value)) {
                callback(new Error("排序只能是非负整数且不能超出10位"));
            } else {
                callback();
            }
        };
        return {

        };
    },
    methods: {

    },
};
