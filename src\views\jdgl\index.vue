<template>
  <div>
    <div v-if="showJd" style="padding: 20px">
      <pageTitle :title="$route.name"></pageTitle>
      <page-container :showLeft="showLeft">
        <template slot="rightContainer">
          <avue-crud
            :option="option"
            :table-loading="loading"
            :data="data"
            :page.sync="page"
            ref="crud"
            v-model="form"
            :updata-planning="updataPlanning"
            :permission="permissionList"
            :before-open="beforeOpen"
            :before-close="beforeClose"
            @row-del="rowDel"
            @row-update="rowUpdate"
            @row-save="rowSave"
            @search-change="searchChange"
            @search-reset="searchReset"
            @selection-change="selectionChange"
            @current-change="currentChange"
            @size-change="sizeChange"
            @refresh-change="refreshChange"
            @on-load="onLoad"
          >
            <template slot="jdSj" slot-scope="scope"> {{ scope.row.jdSj }} </template>
            <template slot="menuLeft">
              <el-button type="primary" size="small" icon="el-icon-plus" @click="addJd">新增</el-button>
              <el-button type="danger" size="small" icon="el-icon-delete" @click="handleDelete">删除</el-button>
            </template>
            <template slot="menu" slot-scope="scope">
              <el-button type="primary" size="text" @click="handleConfig(scope.row, scope.index)">配置</el-button>
              <el-button type="primary" size="text" @click="handleView(scope.row, scope.index)">查看</el-button>
              <el-button type="primary" size="text" @click="handleEdit(scope.row, scope.index)">编辑</el-button>
              <el-button type="danger" style="color: #e9473a" size="text" @click="handleDel(scope.row, scope.index)">删除</el-button>
            </template>
          </avue-crud>
        </template>
      </page-container>
    </div>
    <classify v-if="showClassify" :jdData="jdData" :status="showClassify" backStr="jdgl" @backPage="backPage" />
  </div>
</template>

<script>
import { mapGetters } from "vuex"
import { orgLazyTreeMixin } from "@global/mixins/organization.js"
import { planningMixins } from "@global/mixins/planning.js"
import classify from "../classify/index.vue"
import { jdsearch, jdsave, jdremove, jdupdate } from "../../api/jdgl"
import { mapState } from "vuex"
import website from "@global/config/website.js"
export default {
  name: "jdgl",
  components: { classify },
  mixins: [orgLazyTreeMixin, planningMixins],
  data() {
    return {
      jdData: {},
      showJd: true,
      showClassify: false,
      showLeft: false,
      form: {},
      selectionList: [],
      query: {},
      loading: true,
      page: {
        pageSize: 200,
        currentPage: 1,
        total: 0
      },
      option: {
        searchMenuSpan: 6,
        searchLabelWidth: 100,
        searchMenuBtnAreaSpan: 4,
        searchMenuPosition: "left",
        menuWidth: 260,
        searchShow: true,
        height: "auto",
        calcHeight: 290,
        border: false,
        index: true,
        tip: false,
        indexLabel: "序号",
        indexWidth: "60",
        selection: true,
        formPlanningBtn: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        dialogType: "crudForm",
        align: "center",
        column: [
          {
            label: "节点名称",
            labelPosition: "top",
            search: true,
            maxlength: 100,
            span: 12,
            searchSpan: 6,
            showWordLimit: true,
            prop: "jdMc",
            rules: [
              {
                required: true,
                message: "请输入节点名称",
                trigger: "blur"
              }
            ]
          },
          {
            label: "节点时间",
            prop: "jdSj",
            labelPosition: "top",
            type: "daterange",
            format: "yyyy/MM/dd",
            valueFormat: "yyyy/MM/dd",
            span: 12,
            rules: [
              {
                required: true,
                message: "请输入节点时间",
                trigger: "blur"
              }
            ]
          },
          {
            label: "是否在时间轴显示",
            prop: "sjzXs",
            overHidden: true,
            type: "radio",
            hide: true,
            span: 12,
            labelWidth: "150px",
            dicData: [
              {
                label: "显示",
                value: 1
              },
              {
                label: "隐藏",
                value: 0
              }
            ],
            rules: [
              {
                required: true,
                message: "请选择是否在时间轴显示",
                trigger: "blur"
              }
            ]
          }
        ]
      },
      data: [],
      type: ""
    }
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    ...mapState({
      zdgzPageType: (state) => state.common.zdgzPageType
    }),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.job_add, false),
        viewBtn: this.vaildData(this.permission.job_view, false),
        delBtn: this.vaildData(this.permission.job_delete, false),
        editBtn: this.vaildData(this.permission.job_edit, false)
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach((ele) => {
        ids.push(ele.id)
      })
      return ids.join(",")
    }
  },
  created() {},
  methods: {
    backPage(data) {
      console.log(data)
      this.showJd = true
      this.showClassify = false
    },
    // 刷新组织机构
    addJd() {
      this.$refs.crud.rowAdd()
    },
    rowSave(row, done, loading) {
      let pjlx = this.zdgzPageType[0] == "zxpj" ? website.pjlxObj[this.zdgzPageType[1]] : website.pjlxObj[this.zdgzPageType[0]]
      let sj = row.jdSj.join("-")
      jdsave({
        pjlx: pjlx,
        jdMc: row.jdMc,
        jdSj: sj,
        sjzXs: row.sjzXs
      })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!"
          })
          done()
        })
        .catch(() => {
          loading()
        })
    },
    rowUpdate(row, index, done, loading) {
      let pjlx = this.zdgzPageType[0] == "zxpj" ? website.pjlxObj[this.zdgzPageType[1]] : website.pjlxObj[this.zdgzPageType[0]]
      let sj = row.jdSj.join("-")
      jdupdate({
        id: row.id,
        pjlx: pjlx,
        jdMc: row.jdMc,
        jdSj: sj,
        sjzXs: row.sjzXs
      })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!"
          })
          done()
        })
        .catch(() => {
          loading()
        })
    },
    // 查看文件
    handleView(row, index) {
      this.$refs.crud.rowView(row, index)
    },
    // 配置
    handleConfig(row, index) {
      console.log(row)
      this.showJd = false
      this.showClassify = true
      this.jdData = row
    },
    // 单个删除
    handleDel(row, index) {
      this.$confirm("确定将选择数据删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return jdremove({ ids: row.id })
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: "success",
            message: "操作成功!"
          })
        })
    },
    // 编辑
    handleEdit(row, index) {
      let time = row.jdSj.split("-")
      if (time.length == 1) {
        time = [time[0], time[0]]
      }
      row.jdSj = time
      this.$refs.crud.rowEdit(row, index)
    },
    // 批量删除
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据")
        return
      }
      this.$confirm("确定将选择数据删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return jdremove({ ids: this.ids })
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: "success",
            message: "操作成功!"
          })
          this.$refs.crud.toggleSelection()
        })
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    beforeOpen(done, type) {
      this.showLeft = false
      this.type = type
      done()
    },
    beforeClose(done) {
      this.showLeft = false
      this.onLoad(this.page)
      done()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      console.log(page, params)
      this.loading = true
      let searchParams = {
        current: page.currentPage,
        size: page.pageSize,
        jdMc_like: params.jdMc || ""
      }
      jdsearch(searchParams).then((res) => {
        this.page.total = res.data.data.total
        this.data = res.data.data.records
        this.loading = false
        this.selectionClear()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.tree--node {
  position: relative;
  width: 100%;
  &__dropdown {
    position: absolute;
    right: 10px;
    top: 0;
  }
}
</style>
