<template>
  <basic-container-form class="actionForm">
    <template slot="formBtn">
      <head-form :goBack="goBack">
        <el-button type="primary" @click="rowSave()" v-if="type === 'add'">提 交</el-button>
        <el-button type="primary" @click="rowUpdate()" v-if="type === 'edit'">修 改</el-button>
        <el-button @click="goBack" v-if="type === 'view'">取 消</el-button>
        <el-button @click="cancel" v-if="type === 'add' || type === 'edit'">取 消</el-button>
      </head-form>
    </template>
    <avue-form :option="option" v-model="form" ref="zlForm">
      <template slot-scope="{ size }" slot="zlDz">
        <el-upload class="upload-demo" drag :before-upload="beforeUpload" :http-request="uploadFile" :before-remove="beforeRemove" :on-success="handleSuccess" :file-list="fileList" :limit="1">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em><br />单个文件大小不得超过5个G</div>
        </el-upload>
      </template>
    </avue-form>
  </basic-container-form>
</template>

<script>
import { mapGetters, mapState } from "vuex"
import { wxListUpdate, wxListSave } from "../../api/wxzl"
import website from "@global/config/website.js"
import axios from "axios"

export default {
  props: {
    type: {
      type: String,
      default: "add"
    },
    detailData: {
      type: Object,
      default: () => ({})
    },
    goBack: {
      type: Function
    },
    treeId: {
      type: String,
      default: ""
    },
    rowId: {
      type: String,
      default: ""
    },
    zlType: {
      type: Boolean,
      default: false
    }
  },
  mixins: [],
  data() {
    return {
      option: {
        submitBtn: false,
        emptyBtn: false,
        detail: false,
        labelWidth: "110",
        column: [
          {
            label: "资料名称",
            labelPosition: "top",
            search: true,
            maxlength: 100,
            disabled: true,
            showWordLimit: true,
            prop: "zlMc",
            rules: [
              {
                required: true,
                message: "请输入资料名称",
                trigger: "blur"
              }
            ]
          },
          {
            label: "重命名",
            labelPosition: "top",
            maxlength: 100,
            showWordLimit: true,
            prop: "zlCmm"
          },
          {
            label: "排序",
            prop: "zlPx",
            labelPosition: "top",
            type: "number",
            maxlength: 10,
            minRows: 0,
            width: 100,
            rules: [
              {
                required: true,
                message: "请输入排序",
                trigger: "blur"
              }
            ]
          },
          {
            label: "密级",
            prop: "zlMj",
            overHidden: true,
            type: "radio",
            dicUrl: "/api/xpaas-system/dict-biz/dictionary?code=zlk_mj",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            // dicData: [
            //   {
            //     label: "秘密",
            //     value: 0
            //   },
            //   {
            //     label: "机密",
            //     value: 1
            //   }
            // ],
            rules: [
              {
                required: true,
                message: "请选择密级",
                trigger: "change"
              }
            ]
          },
          {
            label: "上传资料",
            prop: "zlDz",
            span: 24
          }
        ]
      },
      form: {},
      fileList: [],
      uploadRequests: {} // 存储上传请求及取消令牌
    }
  },
  computed: {
    ...mapState({
      zdgzPageType: (state) => state.common.zdgzPageType
    })
  },
  created() {},
  mounted() {
    this.form = {}
    if (this.type === "edit" || this.type === "view") {
      this.detailFun()
    }
  },
  methods: {
    detailFun() {
      this.form = this.detailData
      this.fileList = [
        {
          name: this.detailData.zlMc,
          uid: this.detailData.id
        }
      ]
    },
    beforeUpload(file) {
      // 检查文件大小是否超过5GB
      const MAX_SIZE = 5 * 1024 * 1024 * 1024
      const isLessThan5G = file.size <= MAX_SIZE

      if (!isLessThan5G) {
        this.$message({
          type: "warning",
          message: "文件大小超过 5GB 限制，请选择较小的文件"
        })
        return false
      }

      return true
    },

    uploadFile(options) {
      const { file, onProgress, onSuccess } = options
      // 创建 FormData 对象
      const formData = new FormData()
      formData.append("file", file)

      // 创建取消令牌
      const CancelToken = axios.CancelToken
      const source = CancelToken.source()

      // 发送请求
      const request = this.$axios.post("/api/xpaas-resource/oss/endpoint/put-file?code=minio11", formData, {
        headers: {
          "Content-Type": "multipart/form-data"
        },
        cancelToken: source.token,
        onUploadProgress: (progressEvent) => {
          // 计算并更新上传进度
          const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          // 调用onProgress回调
          onProgress({ percent })
        }
      })

      // 保存请求及其取消方法
      this.uploadRequests[file.uid] = {
        cancel: source.cancel,
        request: request
      }

      // 处理请求结果
      request
        .then((response) => {
          this.form.zlDz = response.data.data.name
          this.form.zlMc = response.data.data.originalName
          // 上传成功
          onSuccess(response.data)
          this.$message({
            type: "success",
            message: "文件上传成功！"
          })
          // 从记录中删除该请求
          delete this.uploadRequests[file.uid]
        })
        .catch((error) => {
          // 如果是取消请求导致的错误，不显示错误信息
          if (axios.isCancel(error)) {
            console.log("上传已取消")
          } else {
            // 上传失败
            this.$message({
              type: "error",
              message: `上传已取消`
            })
          }
          // 从记录中删除该请求
          delete this.uploadRequests[file.uid]
        })

      // 返回一个包含 abort 方法的对象
      return {
        abort: () => {
          if (this.uploadRequests[file.uid]) {
            this.uploadRequests[file.uid].cancel("用户手动取消上传")
          }
        }
      }
    },

    handleSuccess(response, file, fileList) {
      this.fileList = fileList
    },

    beforeRemove(file, fileList) {
      return true
    },

    rowSave() {
      this.$refs.zlForm.validate((valid) => {
        if (valid) {
          let pjlx = this.zdgzPageType[0] == "zxpj" ? website.pjlxObj[this.zdgzPageType[1]] : website.pjlxObj[this.zdgzPageType[0]]
          wxListSave({
            wjjId: this.treeId,
            zlMc: this.form.zlMc,
            zlCmm: this.form.zlCmm,
            zlPx: this.form.zlPx,
            zlDz: this.form.zlDz,
            zlMj: this.form.zlMj,
            wxzlLx: this.zlType ? 0 : 1,
            pjLx: pjlx
          }).then((response) => {
            if (response.data.code == 200) {
              this.$message({
                type: "success",
                message: "保存成功"
              })
              this.goBack && this.goBack()
            }
          })
        } else {
          return false
        }
      })
    },

    rowUpdate() {
      this.$refs.zlForm.validate((valid) => {
        if (valid) {
          let pjlx = this.zdgzPageType[0] == "zxpj" ? website.pjlxObj[this.zdgzPageType[1]] : website.pjlxObj[this.zdgzPageType[0]]
          wxListUpdate({
            id: this.form.id,
            wjjId: this.treeId,
            zlMc: this.form.zlMc,
            zlCmm: this.form.zlCmm,
            zlPx: this.form.zlPx,
            zlDz: this.form.zlDz,
            zlMj: this.form.zlMj,
            wxzlLx: this.zlType ? 0 : 1,
            pjLx: pjlx
          }).then((response) => {
            if (response.data.code == 200) {
              this.$message({
                type: "success",
                message: "修改成功"
              })
              this.goBack && this.goBack()
            }
          })
        } else {
          return false
        }
      })
    },

    cancel() {
      // 实现取消逻辑
      this.goBack && this.goBack()
    }
  },

  // 组件销毁前，取消所有未完成的上传
  beforeDestroy() {
    Object.keys(this.uploadRequests).forEach((uid) => {
      if (this.uploadRequests[uid]) {
        this.uploadRequests[uid].cancel("组件销毁，取消上传")
      }
    })
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-upload-list {
  width: 360px;
}
.progress {
  width: 360px;
}
</style>
