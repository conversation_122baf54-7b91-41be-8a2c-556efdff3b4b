<!--
 * @Author: 秀儿
 * @Date: 2022-01-10 14:58:49
 * @LastEditors: 提瓦特冒险
 * @LastEditTime: 2022-12-23 15:34:21
 * @filePurpose: 
-->
<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="X-UA-Compatible" content="chrome=1" />
    <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/element-ui/2.12.0/theme-chalk/index.css" />
    <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/animate/3.5.2/animate.css" />
    <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/iconfont/1.0.0/index.css" />
    <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/avue/2.8.23/index.css" />
    <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/iconfont/font_1066523_6bvkeuqao36.css" />
    <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/iconfont/font_567566_pwc3oottzol.css" />
    <script type="text/javascript" src="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/jquery/jquery-3.5.1.min.js"></script>

    <script src="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/crypto-js/CryptoJS.js"></script>
    <!-- 导入需要的包 （一定要放到index.html中的head标签里） -->
    <script src="<%= htmlWebpackPlugin.options.baseUrl %>util/Sortable.min.js"></script>

    <link rel="icon" href="<%= htmlWebpackPlugin.options.globalThirdUrl %>favicon.png" />
    <link href="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/wangeditor/index.css" rel="stylesheet" />
    <title>xPaas</title>
  </head>

  <body>
    <noscript>
      <strong> 很抱歉，如果没有 JavaScript 支持，xpaasui 将不能正常工作。请启用浏览器的 JavaScript 然后继续。 </strong>
    </noscript>
    <div id="app">
      <div class="avue-home">
        <div class="avue-home__main">
          <!-- <img class="avue-home__loading" src="./svg/loading-spin.svg" alt="loading"> -->
          <div class="avue-home__title">正在加载资源</div>
          <div class="avue-home__sub-title d">初次加载资源可能需要较多时间 请耐心等待</div>
        </div>
        <div class="avue-home__footer"></div>
      </div>
    </div>
    <!-- built files will be auto injected -->
    <script src="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/wangeditor/index.js" charset="utf-8"></script>
    <script src="<%= htmlWebpackPlugin.options.globalThirdUrl %>util/aes.js" charset="utf-8"></script>
    <script src="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/vue/2.6.10/vue.min.js" charset="utf-8"></script>
    <script src="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/vuex/3.1.1/vuex.min.js" charset="utf-8"></script>
    <script src="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/vue-router/3.0.1/vue-router.min.js" charset="utf-8"></script>
    <script src="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/axios/1.0.0/axios.min.js" charset="utf-8"></script>
    <script src="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/element-ui/2.12.0/index.js" charset="utf-8"></script>
    <script src="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/avue/2.8.23/avue.min.js" charset="utf-8"></script>
    <% if (process.env.NODE_ENV==='development' ) { %>
    <script>
      window.__SUB_NODE_ENV__ = "development"
    </script>
    <% } else { %>
    <script>
      window.__SUB_NODE_ENV__ = "production"
    </script>
    <% } %>
    <script src="<%= htmlWebpackPlugin.options.globalThirdUrl %>cdn/envjs.js" charset="utf-8" ignore></script>
  </body>
</html>
