<template>
  <div style="padding: 20px; box-sizing: border-box">
    <pageTitle :title="$route.name">
      <div slot="titleRight" v-if="status && showLeft">
        <el-button size="mini" type="primary" @click="backPage()">返回上级</el-button>
      </div>
    </pageTitle>
    <page-container :showLeft="showLeft">
      <template slot="leftContainer">
        <el-button type="primary" style="width: 98%; margin-bottom: 10px" size="mini" @click="handleNode('rowAdd')">新 建</el-button>
        <avue-tree :option="treeOption" :data="treeData" ref="tree" class="wxzl-tree" @node-click="nodeClick">
          <div class="tree--node" slot-scope="{ node, data }">
            <span>{{ node.label }}</span>
            <el-dropdown placement="right" class="tree--node__dropdown" size="mini" trigger="click">
              <span class="el-dropdown-link">
                <i class="el-icon-setting"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="permission.job_add">
                  <div class="tree--node__dropdown--btn" @click="handleNode('rowAdd', data, node)">
                    <i class="el-icon-plus"></i>
                    <span>添加</span>
                  </div>
                </el-dropdown-item>
                <el-dropdown-item v-if="permission.job_edit">
                  <div class="tree--node__dropdown--btn" @click="handleNode('rowEdit', data, node)">
                    <i class="el-icon-edit-outline"></i>
                    <span>编辑</span>
                  </div>
                </el-dropdown-item>
                <el-dropdown-item v-if="permission.job_edit">
                  <div class="tree--node__dropdown--btn" @click="handleNode('rowCopy', data, node)">
                    <i class="el-icon-edit-outline"></i>
                    <span>复制</span>
                  </div>
                </el-dropdown-item>
                <el-dropdown-item v-if="permission.job_edit">
                  <div class="tree--node__dropdown--btn" @click="handleNode('rowMove', data, node)">
                    <i class="el-icon-edit-outline"></i>
                    <span>移动</span>
                  </div>
                </el-dropdown-item>
                <el-dropdown-item v-if="permission.job_delete">
                  <div class="tree--node__dropdown--btn" @click="handleNode('rowDel', data, node)">
                    <i class="el-icon-close"></i>
                    <span>删除</span>
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </avue-tree>
      </template>
      <template slot="rightContainer">
        <avue-crud
          :option="option"
          :table-loading="loading"
          :data="data"
          :page.sync="page"
          ref="crud"
          v-model="form"
          :updata-planning="updataPlanning"
          :permission="permissionList"
          :before-open="beforeOpen"
          :before-close="beforeClose"
          @row-del="rowDel"
          @row-update="rowUpdate"
          @row-save="rowSave"
          @search-change="searchChange"
          @search-reset="searchReset"
          @selection-change="selectionChange"
          @current-change="currentChange"
          @size-change="sizeChange"
          @refresh-change="refreshChange"
        >
          <template slot="menuLeft">
            <el-button type="primary" size="small" icon="el-icon-plus" @click="addZl">新增</el-button>
            <el-button type="danger" size="small" icon="el-icon-delete" @click="handleDelete">删除</el-button>
          </template>
          <template slot="menu" slot-scope="scope">
            <el-button type="primary" size="text" @click="handleEdit(scope.row, scope.index)">编辑</el-button>
            <el-button type="primary" size="text" @click="handleView(scope.row, scope.index)">查看</el-button>
            <el-button type="danger" style="color: #e9473a" size="text" @click="handleDel(scope.row, scope.index)">删除</el-button>
          </template>
          <template slot="menuCrudForm" slot-scope="{ goBack }">
            <zl-form :rowId="rowId" :type="type" :treeId="treewxId" :detailData="detailData" :zlType="status" :goBack="goBack"></zl-form>
          </template>
        </avue-crud>
        <el-dialog :title="`${zltitle}文件夹`" :visible.sync="dialogVisible" width="40%" :before-close="handleClose">
          <div class="dialog--content">
            <avue-form :option="filesOption" v-model="fileForm" ref="fileForm"> </avue-form>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitFiles()">确 定</el-button>
          </span>
        </el-dialog>

        <el-dialog :title="`文件预览`" :visible.sync="fileDialogVisible" width="60%" top="5vh" before-close="fileHandleClose">
          <div class="view-dialog-content">
            <iframe width="100%" height="100%" :src="fileSrc" frameborder="0"></iframe>
          </div>
        </el-dialog>
      </template>
    </page-container>
  </div>
</template>

<script>
import { mapGetters } from "vuex"
import zlForm from "./zlForm.vue"
import { crudTree } from "../../mixins/crudTree"
import { orgLazyTreeMixin } from "@global/mixins/organization.js"
import { planningMixins } from "@global/mixins/planning.js"
import { splicing } from "@/utils/util.js"
import Buffer from "vue-buffer"
import { mapState } from "vuex"
import { wxlistTree, wxsave, wxupdate, wxremove, wxListSearch, wxListUpdate, wxListRemove, wxListSave, wxcopyFolder, copyFile } from "../../api/wxzl"
import { fllistTree, flsave, flupdate, flremove, flcopyFolder } from "../../api/classify"
import website from "@global/config/website.js"
export default {
  name: "wxzl",
  components: { zlForm },
  props: {
    status: {
      type: Boolean,
      default: false
    },
    backStr: {
      type: String,
      default: "classify"
    }
  },
  mixins: [crudTree, orgLazyTreeMixin, planningMixins],
  data() {
    return {
      fileSrc: "",
      zltitle: "新建",
      dialogVisible: false,
      fileDialogVisible: false,
      showLeft: true,
      form: {},
      selectionList: [],
      query: {},
      loading: true,
      parentId: 0,
      currentId: 0,
      page: {
        pageSize: 200,
        currentPage: 1,
        total: 0
      },
      treeData: [],
      treeOption: {
        nodeKey: "id",
        // defaultExpandAll: true,
        addBtn: false,
        menu: false,
        size: "small",
        // lazy: true,
        // treeLoad: this.orgTreeLoad({
        //   type: 1,
        //   key: "tree"
        // }),
        props: {
          labelText: "标题",
          label: "wjjMc",
          value: "id",
          children: "children"
        }
      },
      fileForm: {
        wjjMc: "",
        wjjPx: "",
        wbljDz: ""
      },
      detailData: {},
      filesOption: {
        submitBtn: false,
        emptyBtn: false,
        detail: false,
        labelWidth: "110",
        column: [
          {
            label: "文件夹名称",
            labelPosition: "top",
            search: true,
            maxlength: 100,
            span: 12,
            showWordLimit: true,
            prop: "wjjMc",
            rules: [
              {
                required: true,
                message: "请输入文件夹名称",
                trigger: "blur"
              }
            ]
          },
          {
            label: "排序",
            prop: "wjjPx",
            labelPosition: "top",
            type: "number",
            maxlength: 10,
            span: 12,
            minRows: 0,
            width: 100,
            rules: [
              {
                required: true,
                message: "请输入排序",
                trigger: "blur"
              }
            ]
          },
          {
            label: "外部链接",
            prop: "wbljDz",
            span: 12
          }
        ]
      },
      option: {
        searchMenuSpan: 6,
        searchLabelWidth: 100,
        searchMenuBtnAreaSpan: 6,
        searchMenuPosition: "left",
        menuWidth: 260,
        searchShow: true,
        height: "auto",
        calcHeight: 290,
        lazy: true,
        tip: false,
        tree: true,
        title: "",
        border: false,
        index: true,
        indexLabel: "序号",
        indexWidth: "60",
        selection: true,
        formPlanningBtn: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        dialogType: "crudForm",
        crudFormSlot: true,
        align: "center",
        column: [
          {
            label: "资料名称",
            labelPosition: "top",
            search: true,
            maxlength: 30,
            showWordLimit: false,
            prop: "zlMc",
            rules: [
              {
                required: true,
                message: "请输入资料名称",
                trigger: "blur"
              }
            ]
          },
          {
            label: "重命名",
            labelPosition: "top",
            maxlength: 30,
            showWordLimit: false,
            prop: "zlCmm"
          },
          {
            label: "排序",
            prop: "zlPx",
            labelPosition: "top",
            type: "number",
            maxlength: 10,
            minRows: 0,
            width: 100,
            rules: [
              {
                required: true,
                message: "请输入排序",
                trigger: "blur"
              }
            ]
          }
        ]
      },
      data: [],
      treewxId: "",
      rowId: "",
      type: ""
    }
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    ...mapState({
      zdgzPageType: (state) => state.common.zdgzPageType
    }),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.job_add, false),
        viewBtn: this.vaildData(this.permission.job_view, false),
        delBtn: this.vaildData(this.permission.job_delete, false),
        editBtn: this.vaildData(this.permission.job_edit, false)
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach((ele) => {
        ids.push(ele.id)
      })
      return ids.join(",")
    }
  },
  created() {},
  mounted() {
    this.treeDataFun()
  },
  methods: {
    backPage() {
      this.$emit("backPage", this.backStr)
    },
    handleClose() {
      this.dialogVisible = false
    },
    reloadTree() {
      this.reloadOrgTree({ key: "tree", ref: "tree" })
    },
    fileHandleClose() {
      this.fileDialogVisible = false
    },
    // 刷新组织机构
    addZl() {
      this.$refs.crud.rowAdd()
    },
    // 左侧数点击
    nodeClick(data) {
      // if (this.treewxId == data.id) {
      //   this.$refs.tree.setCurrentKey(null)
      //   this.treewxId = ""
      // } else {
      this.treewxId = data.id
      // }
      this.onLoad(this.page)
    },
    // 左侧树操作
    handleNode(methods, data, node) {
      this.parentId = ""
      this.currentId = ""
      if (methods == "rowDel") {
        this.deleteOpen(data)
        return
      }

      this.dialogVisible = true
      if (methods == "rowAdd") {
        this.zltitle = "新建"
        this.fileForm = {
          wjjMc: "",
          wjjPx: "",
          wbljDz: ""
        }
        if (data) {
          this.parentId = data.id
          this.currentId = this.$attrs?.issueData?.sjz?.id || ""
        } else {
          // this.parentId = this.$attrs?.issueData?.sjz?.id || ""
          this.currentId = this.$attrs?.issueData?.sjz?.id || ""
        }
      }
      if (methods == "rowEdit") {
        this.zltitle = "编辑"
        this.fileForm = data
        if (data) {
          this.parentId = data.fjWjjId
          this.currentId = this.$attrs?.issueData?.sjz?.id || ""
        }
      }
    },
    submitFiles() {
      let pjlx = this.zdgzPageType[0] == "zxpj" ? website.pjlxObj[this.zdgzPageType[1]] : website.pjlxObj[this.zdgzPageType[0]]
      this.$refs.fileForm.validate((valid, done, msg) => {
        if (valid) {
          this.dialogVisible = false
          // | fjWjjId | string  | false | none | 父级文件夹id
          // | jdId    | string  | true  | none | 节点id
          // | flglLb  | string  | true  | none | 所属分类管理类别
          // | pjLx    | string  | true  | none | 评价类型
          // | wjjMc   | string  | true  | none | 文件夹名称
          // | wjjLx   | string  | false | none | 文件夹类型       | 0: 外部链接, 1: 子文件夹 (根据 `wbljDz` 自动设置) |
          // | wbljDz  | string  | false | none | 外部链接地址
          // | wjjPx   | integer | false | none | 文件夹排序
          let obj = {
            id: this.zltitle == "新建" ? undefined : this.fileForm.id,
            fjWjjId: this.parentId != "" && this.parentId != 0 ? this.parentId : "",
            jdId: this.currentId != "" && this.currentId != 0 ? this.currentId : "",
            flglLb: this.$attrs?.issueData?.classify.dictKey ? this.$attrs?.issueData?.classify.dictKey : "",
            pjLx: pjlx,
            wjjMc: this.fileForm.wjjMc,
            wjjLx: this.fileForm.wbljDz ? 0 : 1,
            wbljDz: this.fileForm.wbljDz ? this.fileForm.wbljDz : "",
            wjjPx: this.fileForm.wjjPx
          }
          if (this.zltitle == "新建") {
            if (this.status) {
              flsave({ ...obj }).then((res) => {
                if (res.data.code == 200) {
                  this.$message({
                    type: "success",
                    message: "操作成功!"
                  })
                  this.treeDataFun()
                }
              })
            } else {
              wxsave({ ...obj }).then((res) => {
                if (res.data.code == 200) {
                  this.$message({
                    type: "success",
                    message: "操作成功!"
                  })
                  this.treeDataFun()
                }
              })
            }
          } else {
            if (this.status) {
              flupdate({ ...obj }).then((res) => {
                if (res.data.code == 200) {
                  this.$message({
                    type: "success",
                    message: "操作成功!"
                  })
                  this.treeDataFun()
                }
              })
            } else {
              wxupdate({ ...obj }).then((res) => {
                if (res.data.code == 200) {
                  this.$message({
                    type: "success",
                    message: "操作成功!"
                  })
                  this.treeDataFun()
                }
              })
            }
          }
          done()
        } else {
          return false
        }
      })
    },
    treeDataFun() {
      if (this.status) {
        let pjlx = this.zdgzPageType[0] == "zxpj" ? website.pjlxObj[this.zdgzPageType[1]] : website.pjlxObj[this.zdgzPageType[0]]
        fllistTree({
          jdId: this.$attrs?.issueData?.sjz?.id,
          flglLb: this.$attrs?.issueData.classify.dictKey,
          pjlx: pjlx
        }).then((res) => {
          if (res.data.code == 200) {
            this.treeData = res.data.data
            if (res.data.data.length > 0) {
              this.treewxId = this.treeData[0].id
              this.$nextTick(() => {
                this.$refs.tree.setCurrentKey(this.treeData[0].id)
              })
              this.page.currentPage = 1
              this.onLoad(this.page)
            } else {
              this.loading = false
            }
          }
        })
      } else {
        wxlistTree().then((res) => {
          if (res.data.code == 200) {
            this.treeData = res.data.data
            if (res.data.data.length > 0) {
              this.$nextTick(() => {
                this.$refs.tree.setCurrentKey(this.treeData[0].id)
              })
              this.treewxId = this.treeData[0].id
              this.page.currentPage = 1
              this.onLoad(this.page)
            } else {
              this.loading = false
            }
          }
        })
      }
    },
    // 删除左侧树
    deleteOpen(row) {
      console.log("row", row)
      const html = "<p>确定要删除吗？</p>"
      this.$confirm(html, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true
      })
        .then(() => {
          if (this.status) {
            flremove({ ids: row.id }).then((res) => {
              if (res.data.code == 200) {
                this.$message({
                  type: "success",
                  message: "操作成功!"
                })
                // this.treeDataFun()
                this.delDeep(this.treeData, row)
              }
            })
          } else {
            wxremove({ ids: row.id }).then((res) => {
              if (res.data.code == 200) {
                this.$message({
                  type: "success",
                  message: "操作成功!"
                })
                // this.treeDataFun()
                this.delDeep(this.treeData, row)
              }
            })
          }
        })
        .catch(() => {})
    },
    // 递归左侧树
    delDeep(list, target) {
      list.forEach((item, index) => {
        if (item.id == target.id) {
          list.splice(index, 1)
        }
        if (item.children && item.children.length != 0) {
          this.delDeep(item.children, target)
        }
      })
    },
    // 查看文件
    handleView(row, index) {
      let url = Buffer(splicing(row.zlDz)).toString("base64")
      this.fileSrc = window._CONFIG.onlinePreviewUrl + "/onlinePreview?url=" + encodeURIComponent(url)
      if (this.fileSrc) {
        this.fileDialogVisible = true
      }
    },
    // 单个删除
    handleDel(row, index) {
      this.$confirm("确定将选择数据删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return wxListRemove({ ids: row.id })
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: "success",
            message: "操作成功!"
          })
        })
    },
    // 编辑
    handleEdit(row, index) {
      this.$refs.crud.rowEdit(row, index)
      this.detailData = row
    },
    // 批量删除
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据")
        return
      }
      this.$confirm("确定将选择数据删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return wxListRemove({ ids: this.ids })
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: "success",
            message: "操作成功!"
          })
          this.$refs.crud.toggleSelection()
        })
    },
    searchReset() {
      this.query = {}
      // this.treewxId = ""
      // this.$refs.tree.setCurrentKey(null)
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    beforeOpen(done, type) {
      this.showLeft = false
      this.type = type
      done()
    },
    beforeClose(done) {
      this.showLeft = true
      this.onLoad(this.page)
      done()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = false
      let treewxId = this.treewxId ? this.treewxId : ""
      wxListSearch({
        wjjId: treewxId,
        wxzlLx: this.status ? 0 : 1,
        zlMc: params.zlMc,
        current: page.currentPage,
        size: page.pageSize
      }).then((res) => {
        if (res.data.code == 200) {
          console.log(res.data.data)
          if (JSON.stringify(res.data.data) != "{}") {
            this.page.total = res.data.data.total
            this.data = res.data.data.records
            this.loading = false
            this.selectionClear()
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.tree--node {
  position: relative;
  width: 100%;
  &__dropdown {
    position: absolute;
    right: 10px;
    top: 0;
  }
}
.view-dialog-content {
  width: 100%;
  height: 70vh;
}
::v-deep .wxzl-tree {
  &.avue-tree {
    height: calc(100% - 45px) !important;
    .avue-tree__content {
      height: calc(100vh - 420px);
    }
  }
}
</style>
