<template>
  <div>
    <div style="padding: 20px; box-sizing: border-box" v-if="showSjz">
      <pageTitle :title="$route.name"> </pageTitle>
      <basic-container>
        <template>
          <div class="sjz-container">
            <template v-if="!noData">
              <div class="gantt" v-for="(yearItem, index) in ganttList" :key="index">
                <div class="gantt-inner">
                  <div class="cell">
                    <div class="header">
                      <div class="year">{{ yearItem.year }}</div>
                      <div class="row month-wrapper">
                        <div class="month" v-for="(item, index) in monthDiffResult" :key="index">
                          <div class="txt">{{ item }}</div>
                        </div>
                      </div>
                    </div>
                    <div class="content" v-loading="jdLoading">
                      <div class="item" v-for="(item, i) in yearItem.ganttChild" :key="i">
                        <div class="gantt-progress">
                          <!-- <el-popover placement="left" trigger="hover" ref="popoverRef"> -->
                          <div class="popover-centent" :style="jdStyle(item)">
                            <div class="popover-centent-after" v-if="!isMonthGreaterThanNine(item.jdksrq)"></div>
                            <div class="popover-centent-before" v-else></div>
                            <div style="display: flex; justify-content: space-between; align-items: center; box-sizing: border-box; flex: 1">
                              <span style="font-weight: bolder; text-overflow: ellipsis; overflow: hidden; white-space: nowrap; width: 120px">{{ item.jdMc }}</span>
                              <span style="font-weight: bolder; color: #3688ec; margin-left: 10px" @click="handleClick(item)">查看</span>
                            </div>
                            <div>
                              <span style="font-weight: bolder">{{ item.jdksrq }} ~ {{ item.jdjsrq }}</span>
                            </div>
                          </div>
                          <el-progress
                            slot="reference"
                            class="ztjd"
                            :style="'left:' + item.startPosition + 'px;width:' + item.length + 'px'"
                            :color="'#f49924'"
                            :text-inside="true"
                            :format="
                              () => {
                                return ''
                              }
                            "
                            :stroke-width="15"
                            :show-text="true"
                            :percentage="100"
                          >
                          </el-progress>
                          <!-- </el-popover> -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <div class="no-data" v-if="noData">
              <empty />
            </div>
          </div>
        </template>
      </basic-container>
    </div>
    <classify v-if="showClassify" :jdData="jdData" :status="showClassify" backStr="sjz" @backPage="backPage" />
  </div>
</template>

<script>
import classify from "../classify/index.vue"
import { listBySjz } from "@/api/sjz/index.js"
import { mapState } from "vuex"
import website from "@global/config/website"
export default {
  name: "sjz",
  components: { classify },
  data() {
    return {
      jdShow: false,
      jdData: {},
      showSjz: true,
      showClassify: false,
      xmYear: "",
      jdLoading: false,
      noData: false,
      ganttList: [],
      originalZIndexMap: new Map(),
      monthDiffResult: ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"]
    }
  },
  computed: {
    ...mapState({
      zdgzPageType: (state) => state.common.zdgzPageType
    }),
    jdStyle() {
      return (item) => {
        if (!this.isMonthGreaterThanNine(item.jdksrq)) {
          return `left: ${item.startPosition + item.length + 10}px`
        } else {
          return `left: ${item.startPosition - 190}px`
        }
      }
    }
  },
  mounted() {
    console.log(this.zdgzPageType)
    this.initData()
  },
  methods: {
    isMonthGreaterThanNine(dateStr) {
      if (!dateStr) return false

      // 使用正则表达式匹配日期格式中的月份
      // 匹配 YYYY-MM-DD 或 YYYY/MM/DD 格式
      const monthMatch = dateStr.match(/\d{4}[-\/](1[0-2]|0?[1-9])[-\/]/)

      if (monthMatch && monthMatch[1]) {
        const month = parseInt(monthMatch[1], 10)
        return month > 9
      }
      return false
    },
    //初始化数据
    initData() {
      this.getGanttData()
    },
    backPage() {
      this.showSjz = true
      this.showClassify = false
      this.jdShow = false
      this.initData()
    },
    handleClick(item) {
      console.log(item)
      this.jdData = item
      this.showSjz = false
      this.showClassify = true
    },
    getDicColor(type) {
      if (type == "1") {
        return "#70b603"
      }
      if (type == "2") {
        return "#00bfbf"
      }
      if (type == "3") {
        return "#f59a23"
      }
      if (type == "4") {
        return "#d9001b"
      }
      return "#333"
    },
    formatDateWithPadding(dateStr) {
      if (!dateStr) return ""

      // 先将所有斜杠替换为横杠
      let formattedDate = dateStr.replace(/\//g, "-")

      // 拆分日期
      let parts = formattedDate.split("-")

      // 如果格式是 YYYY-M-D，补齐月和日
      if (parts.length === 3) {
        const year = parts[0]
        const month = parts[1].padStart(2, "0")
        const day = parts[2].padStart(2, "0")
        return `${year}-${month}-${day}`
      }
      // 如果格式是 YYYY-M，只补齐月
      else if (parts.length === 2) {
        const year = parts[0]
        const month = parts[1].padStart(2, "0")
        return `${year}-${month}`
      }

      // 其他情况直接返回原值
      return formattedDate
    },
    getGanttData() {
      this.ganttList = []
      this.jdLoading = true
      let pjlx = this.zdgzPageType[0] == "zxpj" ? website.pjlxObj[this.zdgzPageType[1]] : website.pjlxObj[this.zdgzPageType[0]]
      listBySjz({
        pjLx: pjlx
      })
        .then((res) => {
          if (res.data.code == 200) {
            this.ganttList = res.data.data.map((item) => {
              let yearObj = {
                year: Object.keys(item)[0],
                ganttChild: Object.values(item)[0]
              }
              yearObj.ganttChild.forEach((childItem) => {
                if (childItem.jdSj != "") {
                  let time = childItem.jdSj.split("-")
                  // 同一天 补全时间格式
                  if (time.length == 1) {
                    time = [time[0], time[0]]
                  }
                  this.$set(childItem, "jdksrq", this.formatDateWithPadding(time[0]))
                  this.$set(childItem, "jdjsrq", this.formatDateWithPadding(time[1]))
                }
              })
              return yearObj
            })
            this.noData = false
            this.jdLoading = false
            this.$nextTick(() => {
              this.calculatePositionAndLength()
            })
          }
        })
        .catch(() => {
          this.jdLoading = false
          this.noData = true
        })
    },

    /**
     * 计算长度
     */
    calculatePositionAndLength() {
      let monthWidthAll = document.querySelector(".month-wrapper").offsetWidth
      this.ganttList.forEach((item) => {
        item.ganttChild.forEach((childItem) => {
          let startDateStr = childItem.jdksrq
          let endDateStr = childItem.jdjsrq
          // 定义日期格式
          const datePattern = /^(\d{4})-(\d{2})-(\d{2})$/
          // 检查输入参数格式是否正确
          if (!datePattern.test(startDateStr) || !datePattern.test(endDateStr)) {
            throw new Error("日期格式不正确，应为 'YYYY-MM-DD' 格式")
            return
          }
          // 解析日期字符串
          const startDateParts = startDateStr.match(datePattern).slice(1).map(Number)
          const endDateParts = endDateStr.match(datePattern).slice(1).map(Number)
          let year = startDateParts[0] // 获取年份
          let isLeapYear = (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0 // 判断是否为闰年
          let daysInYear = isLeapYear ? 366 : 365 // 计算每年天数
          let daysInMonth = [31, isLeapYear ? 29 : 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31] // 每月天数
          let monthWidth = monthWidthAll / 12
          // 计算每个月的 monthDiff
          let monthDiffs = daysInMonth.map((days) => monthWidth / days)
          const startDate = new Date(startDateParts[0], startDateParts[1] - 1, startDateParts[2])
          const endDate = new Date(endDateParts[0], endDateParts[1] - 1, endDateParts[2])
          // 使用对应的 monthDiff
          let startMonth = startDate.getMonth()
          let endMonth = endDate.getMonth()
          let startDay = startDate.getDate()
          let endDay = endDate.getDate()
          // 计算开始和结束位置
          let startPosition = startMonth * monthWidth + startDay * monthDiffs[startMonth]
          let length = (endMonth - startMonth) * monthWidth + (endDay - startDay) * monthDiffs[endMonth]
          // childItem.startPosition = this.capturecutNumber(startPosition)
          let lengthAll = this.capturecutNumber(length)
          // childItem.length = lengthAll == 0 ? 10 : lengthAll
          this.$set(childItem, "startPosition", this.capturecutNumber(startPosition))
          this.$set(childItem, "length", lengthAll <= 15 ? 15 : lengthAll)
          this.jdShow = true
        })
      })
    },
    capturecutNumber(number) {
      let num = number
      let str = num.toString()
      let parts = str.split(".")
      let decimalPart = parts[1] ? parts[1].substring(0, 4) : "0000"
      let resultStr = parts[0] + "." + decimalPart
      let resultNum = parseFloat(resultStr)
      return resultNum
    }
  }
}
</script>

<style scoped lang="scss">
.centerRow {
  display: flex;
  align-items: center;
}

.clice {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 6px 0 3px;
}

.sjz-container {
  width: 100%;
  height: calc(100vh - 290px);
  box-sizing: border-box;
  overflow: auto;
  .no-data {
    height: calc(100vh - 290px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}
.gantt {
  width: 100%;
  box-sizing: border-box;
}

.gantt .gantt-inner {
  display: flex;
  width: 100%;
  max-height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
}
.gantt .gantt-inner {
  .year {
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 20px;
  }
}

.gantt .gantt-inner .row {
  width: 100%;
  display: flex;
  .month {
    width: 100%;
    background-color: #dde3ea;
    & > div {
      border-right: 1px solid #fff;
    }
    &:last-child > div {
      border-right: none;
    }
  }
}
.popover-centent {
  position: absolute;
  top: 7px;
  background-color: #fff;
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  cursor: pointer;
  & > .popover-centent-after {
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent #fff transparent transparent;
  }

  & > .popover-centent-before {
    position: absolute;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent transparent #fff;
  }
}
.gantt .gantt-inner .txt {
  height: 35px;
  line-height: 35px;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  box-sizing: border-box;
}
.gantt .gantt-inner .cell {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.gantt .gantt-inner .header {
  background-color: #f6f7fb;
}

.gantt .gantt-inner .gantt-progress {
  width: 100%;
  height: 70px;
  border-bottom: 1px solid #edeff4;
  box-sizing: border-box;
  position: relative;
}

.gantt .gantt-inner .content .ztjd {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
::v-deep .el-progress-bar__inner {
  line-height: 13px;
  text-align: center;
}
::v-deep .wxzl-container {
  height: 100%;
  .el-row {
    height: calc(100% - 55px);
  }
}
</style>
