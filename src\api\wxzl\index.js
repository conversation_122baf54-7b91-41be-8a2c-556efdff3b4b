import request from "@global/router/axios";

export const wxlistTree = (params) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/wxzlWjj/listTree",
    method: "get",
    params: params
  });
};


export const wxsave = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/wxzlWjj/save",
    method: "post",
    data: data
  });
};


export const wxupdate = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/wxzlWjj/update",
    method: "post",
    data: data
  });
};


export const wxremove = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/wxzlWjj/remove",
    method: "post",
    params: data
  });
};

export const wxListSave = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/zlgl/save",
    method: "post",
    data: data
  });
};

export const wxListRemove = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/zlgl/remove",
    method: "post",
    params: data
  });
};

export const wxListUpdate = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/zlgl/update",
    method: "post",
    data: data
  });
};

export const wxListSearch = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/zlgl/search",
    method: "get",
    params: data
  });
};

export const wxcopyFolder = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/wxzlWjj/copyFolder",
    method: "post",
    data: data
  });
};
export const copyFile = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/zlgl/copyFile",
    method: "post",
    data: data
  });
};
