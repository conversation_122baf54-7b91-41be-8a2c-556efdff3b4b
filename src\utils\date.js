
export function formatDate(d, f) {
  let fmt = f || 'yyyy-MM-dd hh:mm:ss';
  let date = d ? d : new Date();
  let weekArr = ['日', '一', '二', '三', '四', '五', '六'];
  const opt = {
    'y+': date.getFullYear().toString(), // 年
    'M+': (date.getMonth() + 1).toString(), // 月
    'd+': date.getDate().toString(), // 日
    'h+': date.getHours().toString(), // 时
    'm+': date.getMinutes().toString(), // 分
    's+': date.getSeconds().toString(), // 秒
    'W+': date.getDay() // 星期
  };
  let ret;
  for (let k in opt) {
    ret = new RegExp('(' + k + ')').exec(fmt);
    if (ret) {
      fmt = fmt.replace(
        ret[1],
        ret[1].length == 1
          ? k === 'W+'
            ? weekArr[opt[k]]
            : opt[k]
          : opt[k].padStart(ret[1].length, '0')
      )
    }
  }
  return fmt;
}