import request from "@global/router/axios";
import { param } from "jquery";

export const fllistTree = (params) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/flglWjj/listTree",
    method: "get",
    params: params
  });
};


export const flsave = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/flglWjj/save",
    method: "post",
    data: data
  });
};


export const flupdate = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/flglWjj/update",
    method: "post",
    data: data
  });
};

export const flremove = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/flglWjj/remove",
    method: "post",
    params: data
  });
};

export const listPjLxWl = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/flglWjj/listPjLxWl",
    method: "get",
    params: data
  });
};

export const countResources = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/flglWjj/countResources",
    method: "get",
    params: data
  });
};

export const flcopyFolder = (data) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/flglWjj/copyFolder",
    method: "post",
    data: data
  });
};


