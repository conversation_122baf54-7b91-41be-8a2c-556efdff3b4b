<template>
  <div style="height: 100%">
    <div v-if="showClassify" style="padding: 20px; box-sizing: border-box">
      <pageTitle :title="$route.name">
        <div slot="titleRight" v-if="status">
          <el-button size="mini" type="primary" @click="backPage()">返回上级</el-button>
        </div>
      </pageTitle>
      <basic-container>
        <template>
          <div class="classify-container">
            <div class="classify-card">
              <el-card class="box-card" shadow="hover" v-for="item in flgllbData" :key="item.id">
                <div class="box-card__header" slot="header">
                  <span @click="handleClick(item)">{{ item.dictValue }}（{{ item.totalCount || 0 }}）</span>
                  <el-button v-if="status" type="text" @click="addClassify(item)"><i class="el-icon-edit" style="font-size: 20px"></i></el-button>
                </div>
                <div class="box-card__content"></div>
              </el-card>
            </div>
          </div>
          <el-dialog title="添加" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
            <div class="dialog-content">
              <el-form label-width="100px" :model="form" ref="ruleForm" :rules="rules">
                <el-form-item label="">
                  <el-radio-group v-model="form.classifyType" @change="classifyTypeChange">
                    <el-radio :label="0">添加链接</el-radio>
                    <el-radio :label="1">新增子文件夹</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="文件夹名称" prop="classifyName" v-if="form.classifyType === 1">
                  <el-input v-model="form.classifyName" placeholder="请输入文件夹名称"></el-input>
                </el-form-item>
                <el-form-item label="排序" prop="classifySort" v-if="form.classifyType === 1">
                  <el-input v-model="form.classifySort" placeholder="请输入排序"></el-input>
                </el-form-item>
                <el-form-item label="外部链接">
                  <el-input v-model="form.classifyLink" placeholder="请输入外部链接"></el-input>
                </el-form-item>
              </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
              <el-button @click="dialogVisible = false">取 消</el-button>
              <el-button type="primary" @click="confirmClassify">确 定</el-button>
            </span>
          </el-dialog>
        </template>
      </basic-container>
    </div>
    <wxzl class="wxzl-container" v-if="showWxzl" :issueData="issueData" :status="showWxzl" backStr="classify" @backPage="wxzlbackPage" />
  </div>
</template>

<script>
import wxzl from "../wxzl/index.vue"
import { getDictionary } from "@global/api/system/dictbiz.js"
import { mapState } from "vuex"
import { flsave, flupdate, listPjLxWl, countResources } from "../../api/classify"
import website from "@global/config/website.js"
export default {
  name: "classify",
  components: { wxzl },
  props: {
    status: {
      type: Boolean,
      default: false
    },
    backStr: {
      type: String,
      default: "sjz"
    }
  },
  computed: {
    ...mapState({
      zdgzPageType: (state) => state.common.zdgzPageType
    })
  },

  data() {
    return {
      issueData: {},
      copyForm: {},
      flgllbData: [],
      pjlxData: [],
      pklxCountData: [],
      showClassify: true,
      showWxzl: false,
      dialogVisible: false,
      form: {
        classifyType: 1,
        classifyName: "",
        classifySort: "",
        classifyLink: ""
      },
      rules: {
        classifyName: [{ required: true, message: "请输入文件夹名称", trigger: "blur" }],
        classifySort: [
          { required: true, message: "请输入排序", trigger: "blur" },
          { pattern: /^[1-9]\d*$/, message: "请输入正整数" },
          { max: 3, message: "最多输入3位数字" }
        ]
      }
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    backPage() {
      this.$emit("backPage", this.backStr)
    },
    wxzlbackPage() {
      this.showClassify = true
      this.showWxzl = false
      this.initData()
    },
    handleClick(item) {
      this.issueData = {
        sjz: this.$attrs.jdData,
        classify: item
      }
      if (item.wjjLx == "0") {
      } else {
        this.showClassify = false
        this.showWxzl = true
      }
    },
    //初始化数据
    async initData() {
      // 资料库分类管理类别
      let { data: flgllbData } = await getDictionary({
        code: "zlkgl_flgllb"
      })
      if (this.status) {
        this.listPjLxWlFun()
      }
      this.countResourcesFun()
      this.flgllbData = flgllbData.data
    },
    classifyTypeChange(val) {
      if (val == 1) {
        this.form = {
          classifyType: val,
          classifyName: "",
          classifySort: "",
          classifyLink: "",
          flglLb: this.copyForm.dictKey
        }
      } else {
        this.form = {
          id: this.copyForm.ljid,
          classifyType: val,
          classifyName: "",
          classifySort: "",
          classifyLink: this.copyForm.wbljDz,
          flglLb: this.copyForm.dictKey
        }
      }
    },
    listPjLxWlFun() {
      listPjLxWl({
        jdId: this.$attrs.jdData.id || ""
      }).then((res) => {
        if (res.data.code == 200) {
          let pjlx = this.zdgzPageType[0] == "zxpj" ? website.pjlxObj[this.zdgzPageType[1]] : website.pjlxObj[this.zdgzPageType[0]]
          this.flgllbData.forEach((item) => {
            res.data.data.forEach((item2) => {
              if (item.dictKey == item2.flglLb && item2.pjLx == pjlx) {
                this.$set(item, "wbljDz", item2.wbljDz)
                this.$set(item, "wjjLx", item2.wjjLx)
                this.$set(item, "ljid", item2.id)
              }
            })
          })
          this.pjlxData = res.data.data
        }
      })
    },
    countResourcesFun() {
      let pjlx = this.zdgzPageType[0] == "zxpj" ? website.pjlxObj[this.zdgzPageType[1]] : website.pjlxObj[this.zdgzPageType[0]]
      countResources({
        jdId: this.status ? this.$attrs.jdData.id || "" : undefined,
        pjLx: pjlx
      }).then((res) => {
        if (res.data.code == 200) {
          let pjlx = this.zdgzPageType[0] == "zxpj" ? website.pjlxObj[this.zdgzPageType[1]] : website.pjlxObj[this.zdgzPageType[0]]
          this.flgllbData.forEach((item) => {
            res.data.data.forEach((item2) => {
              if (item.dictKey == item2.flglLb && item2.pjLx == pjlx) {
                this.$set(item, "totalCount", item2.totalCount)
              }
            })
          })
          this.pjlxCountData = res.data.data
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    addClassify(item) {
      console.log(item)
      this.dialogVisible = true
      this.copyForm = item
      if (item.ljid) {
        this.form = {
          id: item.ljid,
          classifyType: +item.wjjLx,
          classifyName: "",
          classifySort: "",
          classifyLink: item.wbljDz,
          flglLb: item.dictKey
        }
      } else {
        this.form = {
          classifyType: 1,
          classifyName: "",
          classifySort: "",
          classifyLink: "",
          flglLb: item.dictKey
        }
      }
    },
    confirmClassify() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          console.log(this.form)
          let pjlx = this.zdgzPageType[0] == "zxpj" ? website.pjlxObj[this.zdgzPageType[1]] : website.pjlxObj[this.zdgzPageType[0]]
          let obj = {
            fjWjjId: "",
            id: this.form.id ? this.form.id : undefined,
            jdId: this.$attrs.jdData.id != "" && this.$attrs.jdData.id != 0 ? this.$attrs.jdData.id : "",
            flglLb: this.form.flglLb ? this.form.flglLb : "",
            pjLx: pjlx,
            wjjMc: this.form.classifyName,
            wjjLx: this.form.classifyType,
            wbljDz: this.form.classifyLink ? this.form.classifyLink : "",
            wjjPx: this.form.classifySort
          }
          if (this.form.id) {
            flupdate({ ...obj }).then((res) => {
              if (res.data.code == 200) {
                this.$message({
                  type: "success",
                  message: "操作成功!"
                })
                this.listPjLxWlFun()
                this.countResourcesFun()
                this.dialogVisible = false
              }
            })
          } else {
            flsave({ ...obj }).then((res) => {
              if (res.data.code == 200) {
                this.$message({
                  type: "success",
                  message: "操作成功!"
                })
                this.listPjLxWlFun()
                this.countResourcesFun()
                this.dialogVisible = false
              }
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.classify-container {
  width: 100%;
  height: calc(100vh - 290px);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  .classify-card {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    box-sizing: border-box;

    .box-card {
      width: calc(33.333% - 30px);
      margin: 20px 20px 0 0;
      box-sizing: border-box;
      &:nth-child(3n) {
        margin-right: 0;
      }
    }
  }
  .box-card__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    & > span {
      cursor: pointer;
      &:hover {
        color: rgb(68, 158, 236);
      }
    }
  }
  .box-card__content {
    height: 200px;
  }
}
.wxzl-container {
  height: 100%;
}
</style>
