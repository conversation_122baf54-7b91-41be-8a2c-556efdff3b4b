{"name": "xpaasui-admin", "version": "2.4.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode dev", "dev": "vue-cli-service serve --mode dev", "test": "vue-cli-service serve --mode test", "prod": "vue-cli-service serve --mode production", "build": "vue-cli-service build  --mode production", "build:prod": "vue-cli-service build  --mode production", "build:test": "vue-cli-service build --mode test", "build:dev": "vue-cli-service build --mode dev", "lint": "vue-cli-service lint", "analyz": "npm_config_report=true npm run build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "subPull": "git submodule update --init --recursive --remote"}, "dependencies": {"@fullcalendar/core": "^5.5.0", "@fullcalendar/daygrid": "^5.5.0", "@fullcalendar/interaction": "^5.5.0", "@fullcalendar/list": "^5.5.0", "@fullcalendar/resource-timeline": "^5.5.0", "@fullcalendar/timegrid": "^5.5.0", "@fullcalendar/vue": "^5.5.0", "@micro-zoe/micro-app": "^0.8.10", "avue-plugin-ueditor": "^0.2.17", "awe-dnd": "^0.3.4", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "bpmn-js": "^7.2.1", "classlist-polyfill": "^1.2.0", "core-js": "^2.6.11", "dayjs": "^1.11.13", "echarts": "^5.0.1", "echarts-liquidfill": "^3.0.0", "element-ui": "^2.12.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jquery": "^3.5.1", "js-base64": "^2.5.1", "js-cookie": "^2.2.0", "json-beautifier": "^1.0.3", "jspdf": "^2.5.1", "jszip": "^3.7.1", "mockjs": "^1.0.1-beta3", "monaco-editor": "^0.23.0", "node-gyp": "^5.0.6", "nprogress": "^0.2.0", "portfinder": "^1.0.23", "script-loader": "^0.7.2", "v-jsoneditor": "^1.4.1", "vue": "^2.6.10", "vue-axios": "^2.1.2", "vue-buffer": "0.0.1", "vue-clipboard2": "^0.3.1", "vue-i18n": "^8.7.0", "vue-router": "^3.0.1", "vuedraggable": "^2.24.0", "vuex": "^3.1.1"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-proposal-optional-chaining": "^7.12.1", "@vue/cli-plugin-babel": "^3.1.1", "@vue/cli-plugin-eslint": "^3.1.5", "@vue/cli-service": "^3.1.4", "babel-eslint": "10.0.1", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "node-sass": "^4.12.0", "sass-loader": "^7.0.1", "script-ext-html-webpack-plugin": "2.1.3", "vue-template-compiler": "^2.5.17", "vue2-ace-editor": "0.0.15", "webpack-bundle-analyzer": "^3.0.3"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}