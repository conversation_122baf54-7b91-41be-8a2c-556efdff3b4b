/*
 * @Author: 碎颅锤本锤
 * @Date: 2022-03-03 16:55:41
 * @LastEditors: 碎颅锤本锤
 * @LastEditTime: 2022-06-30 15:01:30
 * @FilePath: \xpaas-ui-child-manage\src\views\application\mixins\crudTree.js
 * @filePurpose: mixins文件  带有树的
 */
export const crudTree = {
  data() {
    return {
      showLeft: true,
      parentId: "",
      avueTree: true,
      componentName: "actionForm",
      gztYy: "",
    };
  },
  methods: {
    nodeClick(data) {
      if (this.parentId == data.id) {
        this.$refs.deptTree.setCurrentKey(null);
        this.parentId = ''
        this.gztYy = ''
      } else {
        this.parentId = data.id;
        this.gztYy = data.id;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    beforeClose(done, type) {
      this.showLeft = true;
      if (["edit", "view", "add"].includes(type)) {
        this.page.currentPage = 1;
        this.onLoad(this.page);
        this.refreshTree();
      }
      this.addSub = false;
      this.componentName = 'actionForm'
      this.lbTree && this.lbTree()
      done();
    },
    refreshTree() {
      this.avueTree = false;
      this.$nextTick(() => {
        this.avueTree = true;
      });
    },
  },
};
