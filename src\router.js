/*
 * @Author: 嘻嘻
 * @Date: 2023-01-04 09:31:53
 * @LastEditors: 为了新中国
 * @LastEditTime: 2024-04-22 15:46:32
 * @FilePath: \xpaas-ui-child-zlkgl\src\router.js
 * @filePurpose: 
 */

import Layout from '@global/page/index/';

export default [
    // {
    //     path: '/zhpj',
    //     name: '综合评价',
    //     component: Layout,
    //     children: [
    //         {
    //             path: '/sjz',
    //             name: '时间轴',
    //             component: () =>
    //                 import('@/views/sjz/index')
    //         },
    //     ]
    // },
    // {
    //     path: '/visual/dpst',
    //     name: '大屏视图',
    //     component: () =>
    //         import('@/views/visual/components/dpst')
    // },
    // {
    //     path: '/ksh',
    //     component: Layout,
    //     children: [{
    //         path: 'sjgl',
    //         name: '数据管理',
    //         meta: {
    //             i18n: "sjgl",
    //         },
    //         component: () => import('@/views/ksh/sjgl')
    //     },
    //     ]
    // },
]