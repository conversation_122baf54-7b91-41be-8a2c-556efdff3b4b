<!--
 * @Author: 阿里
 * @Date: 2023-10-30 17:25:56
 * @LastEditors: 阿里
 * @LastEditTime: 2023-11-15 09:31:18
 * @FilePath: \xpaas-ui-child-sjgl\src\common\noData.vue
 * @filePurpose: 
-->
<template>
  <div v-if="noInfo" class="zhezhao">
    <img src="@/assets/images/jxpjNoCharts.png" alt="" />
  </div>
</template>

<script>
export default {
  inject: {
    Home: {
      default: () => ({})
    }
  },
  props: {
    noInfo: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.zhezhao {
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 12;
  // background: rgba(0, 0, 0, 0.5);
  font-size: 0.3rem;
  color: #fff;
  // background-color: #fff;
  align-items: center;
  justify-content: center;
}
</style>
