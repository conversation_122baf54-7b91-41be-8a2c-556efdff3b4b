import request from "@global/router/axios";

export const jdsave = (params) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/zlkjdgl/save",
    method: "post",
    data: params
  });
};

export const jdremove = (params) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/zlkjdgl/remove",
    method: "post",
    params: params
  });
};
export const jdupdate = (params) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/zlkjdgl/update",
    method: "post",
    data: params
  });
};
export const jdsearch = (params) => {
  return request({
    url: process.env.VUE_APP_API_BASE_URL + "/zlkjdgl/search",
    method: "get",
    params: params
  });
};
